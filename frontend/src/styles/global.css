@import 'tailwindcss';
@import "tw-animate-css";

@config "../../tailwind.config.js";

@custom-variant dark (&:is(.dark *));
@custom-variant accessibility (&:is(.accessibility *));

:root {
  --background: oklch(1.00 0 0);
  --foreground: oklch(0.37 0.03 259.73);
  --card: oklch(1.00 0 0);
  --card-foreground: oklch(0.37 0.03 259.73);
  --popover: oklch(1.00 0 0);
  --popover-foreground: oklch(0.37 0.03 259.73);
  --primary: oklch(0.72 0.19 149.58);
  --primary-foreground: oklch(1.00 0 0);
  --secondary: oklch(0.85 0.12 85.05);
  --secondary-foreground: oklch(0.45 0.03 256.80);
  --muted: oklch(0.97 0.00 264.54);
  --muted-foreground: oklch(0.55 0.02 264.36);
  --accent: oklch(0.95 0.05 163.05);
  --accent-foreground: oklch(0.37 0.03 259.73);
  --destructive: oklch(0.64 0.21 25.33);
  --destructive-foreground: oklch(1.00 0 0);
  --border: oklch(0.93 0.01 264.53);
  --input: oklch(0.93 0.01 264.53);
  --ring: oklch(0.72 0.19 149.58);
  --chart-1: oklch(0.72 0.19 149.58);
  --chart-2: oklch(0.70 0.15 162.48);
  --chart-3: oklch(0.60 0.13 163.23);
  --chart-4: oklch(0.51 0.10 165.61);
  --chart-5: oklch(0.43 0.09 166.91);
  --sidebar: oklch(0.95 0.03 236.82);
  --sidebar-foreground: oklch(0.37 0.03 259.73);
  --sidebar-primary: oklch(0.72 0.19 149.58);
  --sidebar-primary-foreground: oklch(1.00 0 0);
  --sidebar-accent: oklch(0.95 0.05 163.05);
  --sidebar-accent-foreground: oklch(0.37 0.03 259.73);
  --sidebar-border: oklch(0.93 0.01 264.53);
  --sidebar-ring: oklch(0.72 0.19 149.58);
  --font-sans: DM Sans, sans-serif;
  --font-serif: Lora, serif;
  --font-mono: IBM Plex Mono, monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
  --shadow: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 2px 4px -2px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 4px 6px -2px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 8px 10px -2px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.25);
}

.dark {
  --background: oklch(0.21 0.04 265.75);
  --foreground: oklch(0.87 0.01 258.34);
  --card: oklch(0.28 0.04 260.03);
  --card-foreground: oklch(0.87 0.01 258.34);
  --popover: oklch(0.28 0.04 260.03);
  --popover-foreground: oklch(0.87 0.01 258.34);
  --primary: oklch(0.77 0.15 163.22);
  --primary-foreground: oklch(0.21 0.04 265.75);
  --secondary: oklch(0.34 0.03 260.91);
  --secondary-foreground: oklch(0.71 0.01 286.07);
  --muted: oklch(0.28 0.04 260.03);
  --muted-foreground: oklch(0.55 0.02 264.36);
  --accent: oklch(0.37 0.03 259.73);
  --accent-foreground: oklch(0.71 0.01 286.07);
  --destructive: oklch(0.64 0.21 25.33);
  --destructive-foreground: oklch(0.21 0.04 265.75);
  --border: oklch(0.45 0.03 256.80);
  --input: oklch(0.45 0.03 256.80);
  --ring: oklch(0.77 0.15 163.22);
  --chart-1: oklch(0.77 0.15 163.22);
  --chart-2: oklch(0.78 0.13 181.91);
  --chart-3: oklch(0.72 0.19 149.58);
  --chart-4: oklch(0.70 0.15 162.48);
  --chart-5: oklch(0.60 0.13 163.23);
  --sidebar: oklch(0.28 0.04 260.03);
  --sidebar-foreground: oklch(0.87 0.01 258.34);
  --sidebar-primary: oklch(0.77 0.15 163.22);
  --sidebar-primary-foreground: oklch(0.21 0.04 265.75);
  --sidebar-accent: oklch(0.37 0.03 259.73);
  --sidebar-accent-foreground: oklch(0.71 0.01 286.07);
  --sidebar-border: oklch(0.45 0.03 256.80);
  --sidebar-ring: oklch(0.77 0.15 163.22);
  --font-sans: DM Sans, sans-serif;
  --font-serif: Lora, serif;
  --font-mono: IBM Plex Mono, monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
  --shadow: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 2px 4px -2px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 4px 6px -2px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 8px 10px -2px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.25);
}

/* Версия для слабовидящих */
.accessibility {
  --background: oklch(1.00 0 0); /* Белый фон */
  --foreground: oklch(0.00 0 0); /* Черный текст */
  --card: oklch(1.00 0 0);
  --card-foreground: oklch(0.00 0 0);
  --popover: oklch(1.00 0 0);
  --popover-foreground: oklch(0.00 0 0);
  --primary: oklch(0.00 0 0); /* Черный для основных элементов */
  --primary-foreground: oklch(1.00 0 0); /* Белый текст на черном */
  --secondary: oklch(0.95 0 0); /* Светло-серый */
  --secondary-foreground: oklch(0.00 0 0);
  --muted: oklch(0.90 0 0);
  --muted-foreground: oklch(0.00 0 0);
  --accent: oklch(0.85 0 0);
  --accent-foreground: oklch(0.00 0 0);
  --destructive: oklch(0.00 0 0);
  --destructive-foreground: oklch(1.00 0 0);
  --border: oklch(0.00 0 0); /* Черные границы */
  --input: oklch(1.00 0 0);
  --ring: oklch(0.00 0 0);

  /* Убираем все тени в режиме доступности */
  --shadow-2xs: none;
  --shadow-xs: none;
  --shadow-sm: none;
  --shadow: none;
  --shadow-md: none;
  --shadow-lg: none;
  --shadow-xl: none;
  --shadow-2xl: none;
}

/* Стили для версии слабовидящих */
.accessibility {
  /* Базовый размер шрифта */
  font-size: 18px;
  line-height: 1.6;
}

.accessibility * {
  /* Убираем анимации */
  animation: none !important;
  transition: none !important;
  transform: none !important;

  /* Убираем градиенты и фоновые изображения */
  background-image: none !important;
  background-gradient: none !important;
}

/* Увеличиваем размер текста, но сохраняем структуру */
.accessibility p,
.accessibility span,
.accessibility div,
.accessibility a,
.accessibility li,
.accessibility td,
.accessibility th {
  font-size: max(18px, 1.125em) !important;
  line-height: 1.6 !important;
}

/* Заголовки остаются пропорциональными */
.accessibility h1 {
  font-size: max(32px, 2em) !important;
}

.accessibility h2 {
  font-size: max(28px, 1.75em) !important;
}

.accessibility h3 {
  font-size: max(24px, 1.5em) !important;
}

.accessibility h4,
.accessibility h5,
.accessibility h6 {
  font-size: max(20px, 1.25em) !important;
}

/* Увеличенные интерактивные элементы */
.accessibility button,
.accessibility a[href],
.accessibility input,
.accessibility select,
.accessibility textarea {
  min-height: 48px !important;
  padding: 12px 16px !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  border: 2px solid black !important;
  background-color: white !important;
  color: black !important;
}

/* Кнопки в режиме доступности */
.accessibility button {
  background-color: white !important;
  color: black !important;
  border: 2px solid black !important;
  border-radius: 4px !important;
}

.accessibility button:hover {
  background-color: black !important;
  color: white !important;
}

/* Ссылки в режиме доступности */
.accessibility a[href] {
  color: black !important;
  text-decoration: underline !important;
  border-bottom: 2px solid black !important;
  padding: 4px 8px !important;
}

.accessibility a[href]:hover {
  background-color: black !important;
  color: white !important;
}

/* Четкие фокусы */
.accessibility *:focus {
  outline: 4px solid black !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 4px black !important;
}

/* Карточки и контейнеры */
.accessibility .card,
.accessibility [class*="card"],
.accessibility .container,
.accessibility [class*="container"] {
  border: 2px solid black !important;
  background-color: white !important;
  margin: 8px 0 !important;
  padding: 16px !important;
}

/* Убираем декоративные элементы */
.accessibility img[alt=""],
.accessibility .decoration,
.accessibility .gradient {
  display: none !important;
}

/* Улучшаем layout в режиме доступности */
.accessibility .grid {
  display: block !important;
}

.accessibility .flex {
  display: block !important;
}

.accessibility .flex-col {
  display: block !important;
}

.accessibility .flex-row {
  display: block !important;
}

/* Убираем сложные позиционирования */
.accessibility .absolute,
.accessibility .fixed,
.accessibility .relative {
  position: static !important;
}

/* Упрощаем отступы */
.accessibility .space-y-1 > * + *,
.accessibility .space-y-2 > * + *,
.accessibility .space-y-3 > * + *,
.accessibility .space-y-4 > * + * {
  margin-top: 16px !important;
}

.accessibility .space-x-1 > * + *,
.accessibility .space-x-2 > * + *,
.accessibility .space-x-3 > * + *,
.accessibility .space-x-4 > * + * {
  margin-left: 16px !important;
}

/* Убираем сложные трансформации */
.accessibility .transform,
.accessibility .rotate-1,
.accessibility .rotate-2,
.accessibility .rotate-3,
.accessibility .scale-105,
.accessibility .scale-110 {
  transform: none !important;
}

/* Дополнительные размеры шрифта */
.font-large * {
  font-size: max(20px, 1.25em) !important;
}

.font-extra-large * {
  font-size: max(24px, 1.5em) !important;
}

/* Высокий контраст */
.contrast-high {
  --background: oklch(1.00 0 0) !important;
  --foreground: oklch(0.00 0 0) !important;
  --primary: oklch(0.00 0 0) !important;
  --primary-foreground: oklch(1.00 0 0) !important;
  --border: oklch(0.00 0 0) !important;
}

.contrast-high * {
  background-color: white !important;
  color: black !important;
  border-color: black !important;
}

/* Инвертированные цвета */
.contrast-inverted {
  --background: oklch(0.00 0 0) !important;
  --foreground: oklch(1.00 0 0) !important;
  --primary: oklch(1.00 0 0) !important;
  --primary-foreground: oklch(0.00 0 0) !important;
  --border: oklch(1.00 0 0) !important;
}

.contrast-inverted * {
  background-color: black !important;
  color: white !important;
  border-color: white !important;
}

/* Специальные стили для кнопок в режиме доступности */
.accessibility button:hover,
.contrast-high button:hover,
.contrast-inverted button:hover {
  background-color: gray !important;
  color: white !important;
}

.contrast-inverted button:hover {
  background-color: white !important;
  color: black !important;
}

/* Утилиты для доступности */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus\:not-sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Улучшенная навигация с клавиатуры */
*:focus-visible {
  outline: 3px solid #000;
  outline-offset: 2px;
}

.accessibility *:focus-visible {
  outline: 4px solid #000 !important;
  outline-offset: 3px !important;
}

/* Убираем outline для мыши, оставляем для клавиатуры */
*:focus:not(:focus-visible) {
  outline: none;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

@keyframes sway {
  0% {
    transform: rotate(-5deg);
  }
  100% {
    transform: rotate(5deg);
  }
}
