'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'

interface AccessibilityContextType {
  isAccessibilityMode: boolean
  toggleAccessibilityMode: () => void
  fontSize: 'normal' | 'large' | 'extra-large'
  setFontSize: (size: 'normal' | 'large' | 'extra-large') => void
  contrast: 'normal' | 'high' | 'inverted'
  setContrast: (contrast: 'normal' | 'high' | 'inverted') => void
}

const AccessibilityContext = createContext<AccessibilityContextType | undefined>(undefined)

export function AccessibilityProvider({ children }: { children: React.ReactNode }) {
  const [isAccessibilityMode, setIsAccessibilityMode] = useState(false)
  const [fontSize, setFontSize] = useState<'normal' | 'large' | 'extra-large'>('normal')
  const [contrast, setContrast] = useState<'normal' | 'high' | 'inverted'>('normal')

  // Загружаем настройки из localStorage при инициализации
  useEffect(() => {
    const savedAccessibilityMode = localStorage.getItem('accessibility-mode')
    const savedFontSize = localStorage.getItem('accessibility-font-size')
    const savedContrast = localStorage.getItem('accessibility-contrast')

    if (savedAccessibilityMode === 'true') {
      setIsAccessibilityMode(true)
    }
    
    if (savedFontSize && ['normal', 'large', 'extra-large'].includes(savedFontSize)) {
      setFontSize(savedFontSize as 'normal' | 'large' | 'extra-large')
    }
    
    if (savedContrast && ['normal', 'high', 'inverted'].includes(savedContrast)) {
      setContrast(savedContrast as 'normal' | 'high' | 'inverted')
    }
  }, [])

  // Применяем классы к body при изменении настроек
  useEffect(() => {
    const body = document.body

    // Убираем все предыдущие классы доступности
    body.classList.remove('accessibility', 'font-large', 'font-extra-large', 'contrast-high', 'contrast-inverted')

    if (isAccessibilityMode) {
      body.classList.add('accessibility')

      // Объявляем для скринридеров
      body.setAttribute('aria-label', 'Версия сайта для слабовидящих активна')
    } else {
      body.removeAttribute('aria-label')
    }

    // Применяем размер шрифта
    if (fontSize === 'large') {
      body.classList.add('font-large')
    } else if (fontSize === 'extra-large') {
      body.classList.add('font-extra-large')
    }

    // Применяем контрастность
    if (contrast === 'high') {
      body.classList.add('contrast-high')
    } else if (contrast === 'inverted') {
      body.classList.add('contrast-inverted')
    }
  }, [isAccessibilityMode, fontSize, contrast])

  // Горячие клавиши
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Alt + A - переключить версию для слабовидящих
      if (event.altKey && event.key.toLowerCase() === 'a') {
        event.preventDefault()
        toggleAccessibilityMode()
      }

      // Ctrl + K - открыть поиск (если есть)
      if (event.ctrlKey && event.key.toLowerCase() === 'k') {
        event.preventDefault()
        const searchButton = document.querySelector('.DocSearch-Button') as HTMLButtonElement
        if (searchButton) {
          searchButton.click()
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [toggleAccessibilityMode])

  const toggleAccessibilityMode = () => {
    const newMode = !isAccessibilityMode
    setIsAccessibilityMode(newMode)
    localStorage.setItem('accessibility-mode', newMode.toString())
    
    // Объявляем изменение для скринридеров
    const announcement = newMode 
      ? 'Версия для слабовидящих включена' 
      : 'Обычная версия сайта включена'
    
    // Создаем временный элемент для объявления
    const announcer = document.createElement('div')
    announcer.setAttribute('aria-live', 'polite')
    announcer.setAttribute('aria-atomic', 'true')
    announcer.style.position = 'absolute'
    announcer.style.left = '-10000px'
    announcer.style.width = '1px'
    announcer.style.height = '1px'
    announcer.style.overflow = 'hidden'
    announcer.textContent = announcement
    
    document.body.appendChild(announcer)
    setTimeout(() => document.body.removeChild(announcer), 1000)
  }

  const handleSetFontSize = (size: 'normal' | 'large' | 'extra-large') => {
    setFontSize(size)
    localStorage.setItem('accessibility-font-size', size)
  }

  const handleSetContrast = (newContrast: 'normal' | 'high' | 'inverted') => {
    setContrast(newContrast)
    localStorage.setItem('accessibility-contrast', newContrast)
  }

  return (
    <AccessibilityContext.Provider
      value={{
        isAccessibilityMode,
        toggleAccessibilityMode,
        fontSize,
        setFontSize: handleSetFontSize,
        contrast,
        setContrast: handleSetContrast,
      }}
    >
      {children}
    </AccessibilityContext.Provider>
  )
}

export function useAccessibility() {
  const context = useContext(AccessibilityContext)
  if (context === undefined) {
    throw new Error('useAccessibility must be used within an AccessibilityProvider')
  }
  return context
}
