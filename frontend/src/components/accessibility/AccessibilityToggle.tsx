'use client'

import React, { useState, useContext, useEffect } from 'react'
import { <PERSON>, EyeOff, Setting<PERSON>, Type, Palette } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { useAccessibility } from './AccessibilityProvider'

export function AccessibilityToggle() {
  // Безопасная проверка наличия контекста
  let accessibilityContext
  try {
    accessibilityContext = useAccessibility()
  } catch (error) {
    // Если контекст недоступен, используем локальное состояние
    accessibilityContext = null
  }

  const [localIsAccessibilityMode, setLocalIsAccessibilityMode] = useState(false)
  const [localFontSize, setLocalFontSize] = useState<'normal' | 'large' | 'extra-large'>('normal')
  const [localContrast, setLocalContrast] = useState<'normal' | 'high' | 'inverted'>('normal')
  const [isOpen, setIsOpen] = useState(false)

  // Используем контекст если доступен, иначе локальное состояние
  const isAccessibilityMode = accessibilityContext?.isAccessibilityMode ?? localIsAccessibilityMode
  const fontSize = accessibilityContext?.fontSize ?? localFontSize
  const contrast = accessibilityContext?.contrast ?? localContrast

  const toggleAccessibilityMode = () => {
    if (accessibilityContext) {
      accessibilityContext.toggleAccessibilityMode()
    } else {
      // Локальная реализация
      const newMode = !localIsAccessibilityMode
      setLocalIsAccessibilityMode(newMode)
      localStorage.setItem('accessibility-mode', newMode.toString())

      // Применяем класс к body
      if (newMode) {
        document.body.classList.add('accessibility')
      } else {
        document.body.classList.remove('accessibility')
      }
    }
  }

  const setFontSize = (size: 'normal' | 'large' | 'extra-large') => {
    if (accessibilityContext) {
      accessibilityContext.setFontSize(size)
    } else {
      setLocalFontSize(size)
      localStorage.setItem('accessibility-font-size', size)
    }
  }

  const setContrast = (newContrast: 'normal' | 'high' | 'inverted') => {
    if (accessibilityContext) {
      accessibilityContext.setContrast(newContrast)
    } else {
      setLocalContrast(newContrast)
      localStorage.setItem('accessibility-contrast', newContrast)
    }
  }

  // Загружаем сохраненные настройки при отсутствии контекста
  useEffect(() => {
    if (!accessibilityContext && typeof window !== 'undefined') {
      const savedAccessibilityMode = localStorage.getItem('accessibility-mode')
      const savedFontSize = localStorage.getItem('accessibility-font-size')
      const savedContrast = localStorage.getItem('accessibility-contrast')

      if (savedAccessibilityMode === 'true') {
        setLocalIsAccessibilityMode(true)
        document.body.classList.add('accessibility')
      }

      if (savedFontSize && ['normal', 'large', 'extra-large'].includes(savedFontSize)) {
        setLocalFontSize(savedFontSize as 'normal' | 'large' | 'extra-large')
      }

      if (savedContrast && ['normal', 'high', 'inverted'].includes(savedContrast)) {
        setLocalContrast(savedContrast as 'normal' | 'high' | 'inverted')
      }
    }
  }, [accessibilityContext])

  return (
    <div className="accessibility-toggle">
      {/* Основная кнопка переключения */}
      <Button
        onClick={toggleAccessibilityMode}
        variant={isAccessibilityMode ? "default" : "outline"}
        size="sm"
        className={`
          relative
          ${isAccessibilityMode 
            ? 'bg-black text-white hover:bg-gray-800 border-2 border-black' 
            : 'border-2 border-gray-300 hover:border-gray-500'
          }
        `}
        aria-label={isAccessibilityMode ? 'Выключить версию для слабовидящих' : 'Включить версию для слабовидящих'}
        title={isAccessibilityMode ? 'Выключить версию для слабовидящих' : 'Включить версию для слабовидящих'}
      >
        {isAccessibilityMode ? (
          <EyeOff className="h-4 w-4 mr-2" />
        ) : (
          <Eye className="h-4 w-4 mr-2" />
        )}
        <span className="hidden sm:inline">
          {isAccessibilityMode ? 'Обычная версия' : 'Для слабовидящих'}
        </span>
        <span className="sm:hidden">
          {isAccessibilityMode ? 'А' : 'А+'}
        </span>
      </Button>

      {/* Дополнительные настройки доступности */}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="ml-2 border-2"
            aria-label="Настройки доступности"
            title="Настройки доступности"
          >
            <Settings className="h-4 w-4" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-4" align="end">
          <div className="space-y-4">
            <h3 className="font-semibold text-lg">Настройки доступности</h3>
            
            {/* Размер шрифта */}
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center">
                <Type className="h-4 w-4 mr-2" />
                Размер шрифта
              </label>
              <div className="grid grid-cols-3 gap-2">
                <Button
                  variant={fontSize === 'normal' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFontSize('normal')}
                  className="text-xs"
                >
                  Обычный
                </Button>
                <Button
                  variant={fontSize === 'large' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFontSize('large')}
                  className="text-sm"
                >
                  Большой
                </Button>
                <Button
                  variant={fontSize === 'extra-large' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFontSize('extra-large')}
                  className="text-base"
                >
                  Очень большой
                </Button>
              </div>
            </div>

            {/* Контрастность */}
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center">
                <Palette className="h-4 w-4 mr-2" />
                Контрастность
              </label>
              <div className="grid grid-cols-1 gap-2">
                <Button
                  variant={contrast === 'normal' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setContrast('normal')}
                  className="justify-start"
                >
                  Обычная
                </Button>
                <Button
                  variant={contrast === 'high' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setContrast('high')}
                  className="justify-start bg-white text-black border-2 border-black"
                >
                  Высокий контраст
                </Button>
                <Button
                  variant={contrast === 'inverted' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setContrast('inverted')}
                  className="justify-start bg-black text-white border-2 border-white"
                >
                  Инвертированные цвета
                </Button>
              </div>
            </div>

            {/* Информация */}
            <div className="text-xs text-gray-600 border-t pt-3">
              <p className="mb-2">
                <strong>Версия для слабовидящих</strong> соответствует требованиям ГОСТ Р 52872-2019
              </p>
              <ul className="list-disc list-inside space-y-1">
                <li>Увеличенный размер шрифта (мин. 18px)</li>
                <li>Высокий контраст</li>
                <li>Упрощенная навигация</li>
                <li>Поддержка скринридеров</li>
                <li>Навигация с клавиатуры</li>
              </ul>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
