'use client'

import React, { useState } from 'react'
import { <PERSON>, <PERSON>O<PERSON>, <PERSON><PERSON><PERSON>, Type, Palette } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { useAccessibility } from './AccessibilityProvider'

export function AccessibilityToggle() {
  const {
    isAccessibilityMode,
    toggleAccessibilityMode,
    fontSize,
    setFontSize,
    contrast,
    setContrast,
  } = useAccessibility()
  
  const [isOpen, setIsOpen] = useState(false)

  return (
    <div className="accessibility-toggle">
      {/* Основная кнопка переключения */}
      <Button
        onClick={toggleAccessibilityMode}
        variant={isAccessibilityMode ? "default" : "outline"}
        size="sm"
        className={`
          relative
          ${isAccessibilityMode 
            ? 'bg-black text-white hover:bg-gray-800 border-2 border-black' 
            : 'border-2 border-gray-300 hover:border-gray-500'
          }
        `}
        aria-label={isAccessibilityMode ? 'Выключить версию для слабовидящих' : 'Включить версию для слабовидящих'}
        title={isAccessibilityMode ? 'Выключить версию для слабовидящих' : 'Включить версию для слабовидящих'}
      >
        {isAccessibilityMode ? (
          <EyeOff className="h-4 w-4 mr-2" />
        ) : (
          <Eye className="h-4 w-4 mr-2" />
        )}
        <span className="hidden sm:inline">
          {isAccessibilityMode ? 'Обычная версия' : 'Для слабовидящих'}
        </span>
        <span className="sm:hidden">
          {isAccessibilityMode ? 'А' : 'А+'}
        </span>
      </Button>

      {/* Дополнительные настройки доступности */}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="ml-2 border-2"
            aria-label="Настройки доступности"
            title="Настройки доступности"
          >
            <Settings className="h-4 w-4" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-4" align="end">
          <div className="space-y-4">
            <h3 className="font-semibold text-lg">Настройки доступности</h3>
            
            {/* Размер шрифта */}
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center">
                <Type className="h-4 w-4 mr-2" />
                Размер шрифта
              </label>
              <div className="grid grid-cols-3 gap-2">
                <Button
                  variant={fontSize === 'normal' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFontSize('normal')}
                  className="text-xs"
                >
                  Обычный
                </Button>
                <Button
                  variant={fontSize === 'large' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFontSize('large')}
                  className="text-sm"
                >
                  Большой
                </Button>
                <Button
                  variant={fontSize === 'extra-large' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFontSize('extra-large')}
                  className="text-base"
                >
                  Очень большой
                </Button>
              </div>
            </div>

            {/* Контрастность */}
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center">
                <Palette className="h-4 w-4 mr-2" />
                Контрастность
              </label>
              <div className="grid grid-cols-1 gap-2">
                <Button
                  variant={contrast === 'normal' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setContrast('normal')}
                  className="justify-start"
                >
                  Обычная
                </Button>
                <Button
                  variant={contrast === 'high' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setContrast('high')}
                  className="justify-start bg-white text-black border-2 border-black"
                >
                  Высокий контраст
                </Button>
                <Button
                  variant={contrast === 'inverted' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setContrast('inverted')}
                  className="justify-start bg-black text-white border-2 border-white"
                >
                  Инвертированные цвета
                </Button>
              </div>
            </div>

            {/* Информация */}
            <div className="text-xs text-gray-600 border-t pt-3">
              <p className="mb-2">
                <strong>Версия для слабовидящих</strong> соответствует требованиям ГОСТ Р 52872-2019
              </p>
              <ul className="list-disc list-inside space-y-1">
                <li>Увеличенный размер шрифта (мин. 18px)</li>
                <li>Высокий контраст</li>
                <li>Упрощенная навигация</li>
                <li>Поддержка скринридеров</li>
                <li>Навигация с клавиатуры</li>
              </ul>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
