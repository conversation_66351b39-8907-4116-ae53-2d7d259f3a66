---
import Layout from '@/layouts/Layout.astro'
---

<Layout title="Демо версии для слабовидящих - STOM-LINE" description="Демонстрационная страница для тестирования версии сайта для слабовидящих">
  <div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold text-center mb-8 text-olive-800">
        Демонстрация версии для слабовидящих
      </h1>
      
      <div class="bg-yellow-100 border-l-4 border-yellow-500 p-4 mb-8">
        <h2 class="text-lg font-semibold mb-2">Инструкция по тестированию</h2>
        <ol class="list-decimal pl-6 space-y-2">
          <li>Нажмите кнопку "Для слабовидящих" в правом верхнем углу</li>
          <li>Попробуйте кнопку с шестеренкой для дополнительных настроек</li>
          <li>Протестируйте разные размеры шрифта и контрастность</li>
          <li>Используйте горячую клавишу <kbd class="bg-gray-200 px-2 py-1 rounded">Alt + A</kbd></li>
        </ol>
      </div>

      <!-- Демонстрационный контент -->
      <div class="space-y-8">
        <!-- Заголовки -->
        <section>
          <h2 class="text-2xl font-semibold mb-4">Заголовки разных уровней</h2>
          <h1 class="text-3xl font-bold mb-2">Заголовок H1</h1>
          <h2 class="text-2xl font-semibold mb-2">Заголовок H2</h2>
          <h3 class="text-xl font-medium mb-2">Заголовок H3</h3>
          <h4 class="text-lg font-medium mb-2">Заголовок H4</h4>
        </section>

        <!-- Текст -->
        <section>
          <h2 class="text-2xl font-semibold mb-4">Обычный текст</h2>
          <p class="mb-4">
            Это обычный абзац текста. В режиме для слабовидящих размер шрифта должен увеличиться 
            до минимум 18px, а цвета стать более контрастными.
          </p>
          <p class="mb-4">
            <strong>Жирный текст</strong> и <em>курсивный текст</em> также должны быть хорошо видны.
          </p>
        </section>

        <!-- Кнопки -->
        <section>
          <h2 class="text-2xl font-semibold mb-4">Интерактивные элементы</h2>
          <div class="space-y-4">
            <div class="flex flex-wrap gap-4">
              <button class="bg-olive-600 text-white px-6 py-3 rounded hover:bg-olive-700">
                Основная кнопка
              </button>
              <button class="border border-olive-600 text-olive-600 px-6 py-3 rounded hover:bg-olive-50">
                Вторичная кнопка
              </button>
              <button class="bg-red-600 text-white px-6 py-3 rounded hover:bg-red-700">
                Опасная кнопка
              </button>
            </div>
          </div>
        </section>

        <!-- Ссылки -->
        <section>
          <h2 class="text-2xl font-semibold mb-4">Ссылки</h2>
          <p class="mb-4">
            Вот <a href="/services" class="text-olive-600 hover:text-olive-800 underline">ссылка на услуги</a> и 
            <a href="/specialists" class="text-olive-600 hover:text-olive-800 underline">ссылка на специалистов</a>.
          </p>
        </section>

        <!-- Формы -->
        <section>
          <h2 class="text-2xl font-semibold mb-4">Элементы форм</h2>
          <form class="space-y-4 max-w-md">
            <div>
              <label for="name" class="block text-sm font-medium mb-2">Имя</label>
              <input 
                type="text" 
                id="name" 
                name="name" 
                class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-olive-500"
                placeholder="Введите ваше имя"
              />
            </div>
            <div>
              <label for="email" class="block text-sm font-medium mb-2">Email</label>
              <input 
                type="email" 
                id="email" 
                name="email" 
                class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-olive-500"
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <label for="message" class="block text-sm font-medium mb-2">Сообщение</label>
              <textarea 
                id="message" 
                name="message" 
                rows="4"
                class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-olive-500"
                placeholder="Введите ваше сообщение"
              ></textarea>
            </div>
            <button type="submit" class="bg-olive-600 text-white px-6 py-3 rounded hover:bg-olive-700">
              Отправить
            </button>
          </form>
        </section>

        <!-- Карточки -->
        <section>
          <h2 class="text-2xl font-semibold mb-4">Карточки</h2>
          <div class="grid md:grid-cols-2 gap-6">
            <div class="bg-white p-6 rounded-lg shadow-md border">
              <h3 class="text-xl font-semibold mb-3">Карточка 1</h3>
              <p class="text-gray-600 mb-4">
                Описание первой карточки с некоторым текстом для демонстрации.
              </p>
              <button class="bg-olive-600 text-white px-4 py-2 rounded hover:bg-olive-700">
                Подробнее
              </button>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-md border">
              <h3 class="text-xl font-semibold mb-3">Карточка 2</h3>
              <p class="text-gray-600 mb-4">
                Описание второй карточки с некоторым текстом для демонстрации.
              </p>
              <button class="bg-olive-600 text-white px-4 py-2 rounded hover:bg-olive-700">
                Подробнее
              </button>
            </div>
          </div>
        </section>

        <!-- Списки -->
        <section>
          <h2 class="text-2xl font-semibold mb-4">Списки</h2>
          <div class="grid md:grid-cols-2 gap-6">
            <div>
              <h3 class="text-lg font-medium mb-3">Маркированный список</h3>
              <ul class="list-disc pl-6 space-y-2">
                <li>Первый пункт списка</li>
                <li>Второй пункт списка</li>
                <li>Третий пункт списка</li>
                <li>Четвертый пункт списка</li>
              </ul>
            </div>
            <div>
              <h3 class="text-lg font-medium mb-3">Нумерованный список</h3>
              <ol class="list-decimal pl-6 space-y-2">
                <li>Первый шаг</li>
                <li>Второй шаг</li>
                <li>Третий шаг</li>
                <li>Четвертый шаг</li>
              </ol>
            </div>
          </div>
        </section>

        <!-- Навигация с клавиатуры -->
        <section>
          <h2 class="text-2xl font-semibold mb-4">Тестирование навигации</h2>
          <p class="mb-4">
            Используйте клавишу <kbd class="bg-gray-200 px-2 py-1 rounded">Tab</kbd> для навигации по элементам:
          </p>
          <div class="space-x-4">
            <button class="bg-blue-600 text-white px-4 py-2 rounded">Кнопка 1</button>
            <button class="bg-green-600 text-white px-4 py-2 rounded">Кнопка 2</button>
            <button class="bg-purple-600 text-white px-4 py-2 rounded">Кнопка 3</button>
          </div>
        </section>
      </div>

      <!-- Информация о тестировании -->
      <div class="mt-12 bg-olive-50 p-6 rounded-lg">
        <h2 class="text-xl font-semibold mb-4">Что проверить в режиме доступности:</h2>
        <ul class="list-disc pl-6 space-y-2">
          <li>✅ Размер шрифта увеличился до минимум 18px</li>
          <li>✅ Цвета стали более контрастными (черный на белом)</li>
          <li>✅ Кнопки стали больше (минимум 48x48px)</li>
          <li>✅ Убрались анимации и декоративные элементы</li>
          <li>✅ Появились четкие границы у элементов</li>
          <li>✅ Навигация с клавиатуры работает корректно</li>
          <li>✅ Настройки сохраняются при перезагрузке страницы</li>
        </ul>
      </div>
    </div>
  </div>
</Layout>

<style>
  kbd {
    font-family: monospace;
    font-size: 0.875em;
  }
</style>
