{"name": "astro-app", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "sync-search": "node scripts/sync-search.js", "init-search": "node scripts/sync-search.js init"}, "dependencies": {"@astrojs/mdx": "^4.3.0", "@astrojs/node": "^9.2.2", "@astrojs/react": "^4.3.0", "@docsearch/css": "^3.9.0", "@docsearch/react": "^3.9.0", "@meilisearch/instant-meilisearch": "^0.26.0", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@tailwindcss/vite": "^4.1.7", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-text-align": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@types/animejs": "^3.1.13", "@types/canvas-confetti": "^1.9.0", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "animejs": "^4.0.2", "astro": "^5.8.0", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.14.0", "lucide-react": "^0.487.0", "motion": "^12.14.0", "pocketbase": "^0.26.0", "prettier-plugin-astro": "^0.14.1", "prettier-plugin-tailwindcss": "^0.6.11", "react": "^19.1.0", "react-dom": "^19.1.0", "react-intersection-observer": "^9.16.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.7", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.0", "vaul": "^1.1.2"}}